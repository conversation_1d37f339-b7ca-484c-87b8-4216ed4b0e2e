<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AMS - Airline Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        /* Navigation Bar */
        .navbar {
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            color: white;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover, .nav-links a.active {
            background-color: rgba(255,255,255,0.2);
        }

        /* Main Container */
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* Page Content */
        .page-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .page-content.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .page-title {
            color: #1e3c72;
            font-size: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 1rem;
        }

        /* Form Styles */
        .form-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .section-label {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 1rem;
            display: block;
        }

        .hr-ruler {
            border: none;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            margin: 1rem 0;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            font-weight: 600;
            color: #555;
            margin-bottom: 0.5rem;
            position: relative;
        }

        input, select {
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        input.error {
            border-color: #e74c3c;
            background-color: #fdf2f2;
        }

        input.validation-success {
            border-color: #28a745;
            background-color: #f8fff9;
        }

        input.validation-warning {
            border-color: #ffc107;
            background-color: #fffbf0;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid #28a745;
            display: none;
        }

        /* Buttons */
        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }

        .btn-group {
            text-align: center;
            margin-top: 2rem;
        }

        /* Search Results Table */
        .search-results {
            margin-top: 2rem;
            display: none;
        }

        .search-results.show {
            display: block;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        /* Tooltip */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #2c3e50;
            color: #ecf0f1;
            text-align: left;
            border-radius: 8px;
            padding: 15px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8rem;
            line-height: 1.5;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .password-policy h4 {
            margin: 0 0 10px 0;
            color: #3498db;
            font-size: 0.9rem;
        }

        .password-policy ul {
            margin: 0;
            padding-left: 15px;
        }

        .password-policy li {
            margin-bottom: 5px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem 2rem;
            border-radius: 15px 15px 0 0;
            position: relative;
        }

        .modal-title {
            margin: 0;
            font-size: 1.5rem;
        }

        .close {
            color: white;
            position: absolute;
            top: 1rem;
            right: 1.5rem;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s;
        }

        .close:hover {
            opacity: 0.7;
        }

        .modal-body {
            padding: 2rem;
        }

        /* Payment Section */
        .payment-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1.5rem 0;
            border: 1px solid #ddd;
        }

        .payment-title {
            color: #1e3c72;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }

        .amount-display {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 1rem 0;
        }

        /* Home Page Styles */
        .home-search {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .flight-results {
            display: none;
            margin-top: 2rem;
        }

        .flight-results.show {
            display: block;
        }

        .flight-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .flight-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .flight-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .flight-route {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1e3c72;
        }

        .flight-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #28a745;
        }

        .flight-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .detail-item {
            text-align: center;
        }

        .detail-label {
            font-size: 0.875rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            font-weight: bold;
            color: #333;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .nav-links {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .container {
                padding: 0 1rem;
            }
            
            .modal-content {
                width: 95%;
                margin: 1% auto;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">✈️ AMS</a>
            <ul class="nav-links">
                <li><a href="#" onclick="showPage('home')" class="nav-link active">Home</a></li>
                <li><a href="#" onclick="showPage('register')" class="nav-link">User Registration</a></li>
                <li><a href="#" onclick="showPage('add-carrier')" class="nav-link">Add Carrier</a></li>
                <li><a href="#" onclick="showPage('edit-carrier')" class="nav-link">Edit/Delete Carrier</a></li>
                <li><a href="#" onclick="showPage('add-flight')" class="nav-link">Add Flight</a></li>
                <li><a href="#" onclick="showPage('edit-flight')" class="nav-link">Edit/Delete Flight</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <!-- Home Page - Book Flight -->
        <div id="home" class="page-content active">
            <div class="home-search">
                <h2 class="page-title">Book Your Flight</h2>
                <form class="search-form" onsubmit="searchFlights(event)">
                    <div class="form-group">
                        <label for="search-origin">From</label>
                        <select id="search-origin" required>
                            <option value="">Select Origin</option>
                            <option value="Mumbai">Mumbai</option>
                            <option value="Delhi">Delhi</option>
                            <option value="Bangalore">Bangalore</option>
                            <option value="Chennai">Chennai</option>
                            <option value="Kolkata">Kolkata</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="search-destination">To</label>
                        <select id="search-destination" required>
                            <option value="">Select Destination</option>
                            <option value="Mumbai">Mumbai</option>
                            <option value="Delhi">Delhi</option>
                            <option value="Bangalore">Bangalore</option>
                            <option value="Chennai">Chennai</option>
                            <option value="Kolkata">Kolkata</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="travel-date">Travel Date</label>
                        <input type="date" id="travel-date" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Search Flights</button>
                    </div>
                </form>
            </div>

            <div id="flight-results" class="flight-results">
                <h3>Available Flights</h3>
                <div id="flights-container"></div>
            </div>

            <div id="booking-success" class="success-message"></div>
        </div>

        <!-- User Registration Page -->
        <div id="register" class="page-content">
            <h2 class="page-title">User Registration</h2>
            <form class="form-container" onsubmit="registerUser(event)">
                <div class="form-section">
                    <span class="section-label">Personal Information</span>
                    <hr class="hr-ruler">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-first-name">First Name</label>
                            <input type="text" id="user-first-name" maxlength="50" required>
                            <span class="error-message" id="user-first-name-error">Please enter a valid first name (alphabets only)</span>
                        </div>
                        <div class="form-group">
                            <label for="user-last-name">Last Name</label>
                            <input type="text" id="user-last-name" maxlength="50" required>
                            <span class="error-message" id="user-last-name-error">Please enter a valid last name (alphabets only)</span>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-email">Email Address</label>
                            <input type="email" id="user-email" required>
                            <span class="error-message" id="user-email-error">Please enter a valid email address</span>
                        </div>
                        <div class="form-group">
                            <label for="user-mobile">Mobile Number</label>
                            <input type="tel" id="user-mobile" maxlength="10" required placeholder="Enter 10-digit mobile number">
                            <span class="error-message" id="user-mobile-error">Please enter a valid Indian mobile number (10 digits starting with 6, 7, 8, or 9)</span>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-dob">Date of Birth</label>
                            <input type="date" id="user-dob" required>
                            <span class="error-message" id="user-dob-error">You must be at least 18 years old</span>
                        </div>
                        <div class="form-group">
                            <label for="user-gender">Gender</label>
                            <select id="user-gender" required>
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <span class="section-label">Account Security</span>
                    <hr class="hr-ruler">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-username">Username</label>
                            <input type="text" id="user-username" minlength="4" maxlength="20" required>
                            <span class="error-message" id="user-username-error">Username must be 4-20 characters long</span>
                        </div>
                        <div class="form-group">
                            <label for="user-password" class="tooltip">
                                Password
                                <span class="tooltiptext password-policy">
                                    <h4>Password Requirements:</h4>
                                    <ul>
                                        <li>Minimum 8 characters</li>
                                        <li>At least 1 uppercase letter (A-Z)</li>
                                        <li>At least 1 lowercase letter (a-z)</li>
                                        <li>At least 1 number (0-9)</li>
                                        <li>At least 1 special character (@$!%*?&)</li>
                                    </ul>
                                </span>
                            </label>
                            <input type="password" id="user-password" required>
                            <span class="error-message" id="user-password-error">Password must meet all requirements</span>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="user-confirm-password">Confirm Password</label>
                            <input type="password" id="user-confirm-password" required>
                            <span class="error-message" id="user-confirm-password-error">Passwords do not match</span>
                        </div>
                        <div class="form-group">
                            <label for="user-type">User Type</label>
                            <select id="user-type" required>
                                <option value="">Select User Type</option>
                                <option value="Regular">Regular</option>
                                <option value="Silver">Silver</option>
                                <option value="Gold">Gold</option>
                                <option value="Platinum">Platinum</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">Register User</button>
                    <button type="reset" class="btn btn-secondary">Reset Form</button>
                </div>
            </form>
            
            <div id="register-success" class="success-message"></div>
        </div>

        <!-- Add Carrier Page -->
        <div id="add-carrier" class="page-content">
            <h2 class="page-title">Add Carrier Details</h2>
            <form class="form-container" onsubmit="addCarrier(event)">
                <div class="form-group full-width">
                    <label for="carrier-name">Carrier Name</label>
                    <input type="text" id="carrier-name" maxlength="50" required>
                    <span class="error-message" id="carrier-name-error">Please enter a valid carrier name (alphabets only)</span>
                </div>

                <div class="form-section">
                    <span class="section-label">Discount Percentage</span>
                    <hr class="hr-ruler">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="discount-30days">30 Days Advance Booking (%)</label>
                            <input type="number" id="discount-30days" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="discount-30days-error">Please enter a valid percentage</span>
                        </div>
                        <div class="form-group">
                            <label for="discount-60days">60 Days Advance Booking (%)</label>
                            <input type="number" id="discount-60days" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="discount-60days-error">Please enter a valid percentage</span>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="discount-90days">90 Days Advance Booking (%)</label>
                            <input type="number" id="discount-90days" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="discount-90days-error">Please enter a valid percentage</span>
                        </div>
                        <div class="form-group">
                            <label for="discount-bulk">Bulk Booking (%)</label>
                            <input type="number" id="discount-bulk" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="discount-bulk-error">Please enter a valid percentage</span>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="discount-silver">Silver User (%)</label>
                            <input type="number" id="discount-silver" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="discount-silver-error">Please enter a valid percentage</span>
                        </div>
                        <div class="form-group">
                            <label for="discount-gold">Gold User (%)</label>
                            <input type="number" id="discount-gold" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="discount-gold-error">Please enter a valid percentage</span>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="discount-platinum">Platinum User (%)</label>
                            <input type="number" id="discount-platinum" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="discount-platinum-error">Please enter a valid percentage</span>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <span class="section-label">Refund Percentage</span>
                    <hr class="hr-ruler">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="refund-2days">2 Days Before Travel Date (%)</label>
                            <input type="number" id="refund-2days" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="refund-2days-error">Please enter a valid percentage</span>
                        </div>
                        <div class="form-group">
                            <label for="refund-10days">10 Days Before Travel Date (%)</label>
                            <input type="number" id="refund-10days" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="refund-10days-error">Please enter a valid percentage</span>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="refund-20days">20 Days Or More Before Travel Date (%)</label>
                            <input type="number" id="refund-20days" min="0" max="100" step="0.1" required>
                            <span class="error-message" id="refund-20days-error">Please enter a valid percentage</span>
                        </div>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">Add Carrier</button>
                </div>
            </form>
            
            <div id="add-carrier-success" class="success-message"></div>
        </div>

        <!-- Edit/Delete Carrier Page -->
        <div id="edit-carrier" class="page-content">
            <h2 class="page-title">Edit/Delete Carrier Details</h2>
            
            <div class="form-container">
                <form class="search-form" onsubmit="searchCarrier(event)">
                    <div class="form-group">
                        <label for="edit-carrier-select">Carrier Name</label>
                        <select id="edit-carrier-select" required>
                            <option value="">Select Carrier</option>
                            <option value="Air India">Air India</option>
                            <option value="IndiGo">IndiGo</option>
                            <option value="SpiceJet">SpiceJet</option>
                            <option value="Vistara">Vistara</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Search</button>
                    </div>
                </form>

                <div id="carrier-edit-form" class="search-results">
                    <form onsubmit="updateCarrier(event)">
                        <div class="form-group full-width">
                            <label for="edit-carrier-name">Carrier Name</label>
                            <input type="text" id="edit-carrier-name" readonly>
                        </div>

                        <div class="form-section">
                            <span class="section-label">Discount Percentage</span>
                            <hr class="hr-ruler">
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-discount-60days">60 Days Advance Booking (%)</label>
                                    <input type="number" id="edit-discount-60days" min="0" max="100" step="0.1" required>
                                </div>
                                <div class="form-group">
                                    <label for="edit-discount-90days">90 Days Advance Booking (%)</label>
                                    <input type="number" id="edit-discount-90days" min="0" max="100" step="0.1" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-discount-bulk">Bulk Booking (%)</label>
                                    <input type="number" id="edit-discount-bulk" min="0" max="100" step="0.1" required>
                                </div>
                                <div class="form-group">
                                    <label for="edit-discount-silver">Silver User (%)</label>
                                    <input type="number" id="edit-discount-silver" min="0" max="100" step="0.1" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-discount-gold">Gold User (%)</label>
                                    <input type="number" id="edit-discount-gold" min="0" max="100" step="0.1" required>
                                </div>
                                <div class="form-group">
                                    <label for="edit-discount-platinum">Platinum User (%)</label>
                                    <input type="number" id="edit-discount-platinum" min="0" max="100" step="0.1" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <span class="section-label">Refund Percentage</span>
                            <hr class="hr-ruler">
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-refund-2days">2 Days Before Travel Date (%)</label>
                                    <input type="number" id="edit-refund-2days" min="0" max="100" step="0.1" required>
                                </div>
                                <div class="form-group">
                                    <label for="edit-refund-10days">10 Days Before Travel Date (%)</label>
                                    <input type="number" id="edit-refund-10days" min="0" max="100" step="0.1" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-refund-20days">20 Days Or More Before Travel Date (%)</label>
                                    <input type="number" id="edit-refund-20days" min="0" max="100" step="0.1" required>
                                </div>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button type="submit" class="btn btn-secondary">Edit Carrier</button>
                            <button type="button" class="btn btn-danger" onclick="deleteCarrier()">Delete Carrier</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div id="edit-carrier-success" class="success-message"></div>
        </div>

        <!-- Add Flight Page -->
        <div id="add-flight" class="page-content">
            <h2 class="page-title">Add Flight Details</h2>
            <form class="form-container" onsubmit="addFlight(event)">
                <div class="form-row">
                    <div class="form-group">
                        <label for="flight-carrier">Carrier Name</label>
                        <select id="flight-carrier" required>
                            <option value="">Select Carrier</option>
                            <option value="Air India">Air India</option>
                            <option value="IndiGo">IndiGo</option>
                            <option value="SpiceJet">SpiceJet</option>
                            <option value="Vistara">Vistara</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="flight-origin">Origin</label>
                        <select id="flight-origin" required>
                            <option value="">Select Origin</option>
                            <option value="Mumbai">Mumbai</option>
                            <option value="Delhi">Delhi</option>
                            <option value="Bangalore">Bangalore</option>
                            <option value="Chennai">Chennai</option>
                            <option value="Kolkata">Kolkata</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="flight-destination">Destination</label>
                        <select id="flight-destination" required>
                            <option value="">Select Destination</option>
                            <option value="Mumbai">Mumbai</option>
                            <option value="Delhi">Delhi</option>
                            <option value="Bangalore">Bangalore</option>
                            <option value="Chennai">Chennai</option>
                            <option value="Kolkata">Kolkata</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="flight-airfare">Air Fare (₹)</label>
                        <input type="number" id="flight-airfare" min="1" required>
                        <span class="error-message" id="flight-airfare-error">Please enter a valid fare amount</span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="seat-business">Seat Capacity - Business Class</label>
                        <input type="number" id="seat-business" min="0" required>
                        <span class="error-message" id="seat-business-error">Please enter a valid seat count</span>
                    </div>
                    <div class="form-group">
                        <label for="seat-economy">Seat Capacity - Economy Class</label>
                        <input type="number" id="seat-economy" min="0" required>
                        <span class="error-message" id="seat-economy-error">Please enter a valid seat count</span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="seat-executive">Seat Capacity - Executive Class</label>
                        <input type="number" id="seat-executive" min="0" required>
                        <span class="error-message" id="seat-executive-error">Please enter a valid seat count</span>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">Add Flight</button>
                </div>
            </form>
            
            <div id="add-flight-success" class="success-message"></div>
        </div>

        <!-- Edit/Delete Flight Page -->
        <div id="edit-flight" class="page-content">
            <h2 class="page-title">Edit/Delete Flight Details</h2>
            
            <div class="form-container">
                <form class="search-form" onsubmit="searchFlight(event)">
                    <div class="form-group">
                        <label for="search-flight-carrier">Carrier Name</label>
                        <select id="search-flight-carrier" required>
                            <option value="">Select Carrier</option>
                            <option value="Air India">Air India</option>
                            <option value="IndiGo">IndiGo</option>
                            <option value="SpiceJet">SpiceJet</option>
                            <option value="Vistara">Vistara</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="search-flight-origin">Origin</label>
                        <select id="search-flight-origin" required>
                            <option value="">Select Origin</option>
                            <option value="Mumbai">Mumbai</option>
                            <option value="Delhi">Delhi</option>
                            <option value="Bangalore">Bangalore</option>
                            <option value="Chennai">Chennai</option>
                            <option value="Kolkata">Kolkata</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="search-flight-destination">Destination</label>
                        <select id="search-flight-destination" required>
                            <option value="">Select Destination</option>
                            <option value="Mumbai">Mumbai</option>
                            <option value="Delhi">Delhi</option>
                            <option value="Bangalore">Bangalore</option>
                            <option value="Chennai">Chennai</option>
                            <option value="Kolkata">Kolkata</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Search</button>
                    </div>
                </form>

                <div id="flight-search-results" class="search-results">
                    <h3>Flight Details</h3>
                    <form onsubmit="updateFlight(event)">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-flight-carrier">Carrier Name</label>
                                <input type="text" id="edit-flight-carrier" readonly>
                            </div>
                            <div class="form-group">
                                <label for="edit-flight-origin">Origin</label>
                                <input type="text" id="edit-flight-origin" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-flight-destination">Destination</label>
                                <input type="text" id="edit-flight-destination" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-flight-airfare">Air Fare (₹)</label>
                                <input type="number" id="edit-flight-airfare" min="1" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-seat-business">Seat Capacity - Business Class</label>
                                <input type="number" id="edit-seat-business" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-seat-economy">Seat Capacity - Economy Class</label>
                                <input type="number" id="edit-seat-economy" min="0" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="edit-seat-executive">Seat Capacity - Executive Class</label>
                                <input type="number" id="edit-seat-executive" min="0" required>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button type="submit" class="btn btn-secondary">Edit Flight</button>
                            <button type="button" class="btn btn-danger" onclick="deleteFlight()">Delete Flight</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div id="edit-flight-success" class="success-message"></div>
        </div>
    </div>

    <!-- Booking Modal -->
    <div id="booking-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Complete Your Booking</h2>
                <span class="close" onclick="closeBookingModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="booking-form" onsubmit="processBooking(event)">
                    <div class="form-section">
                        <span class="section-label">Flight Information</span>
                        <hr class="hr-ruler">
                        <div id="selected-flight-info" class="flight-card" style="margin-bottom: 1rem;">
                            <!-- Flight info will be populated here -->
                        </div>
                    </div>

                    <div class="form-section">
                        <span class="section-label">Passenger Information</span>
                        <hr class="hr-ruler">
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="passenger-first-name">First Name</label>
                                <input type="text" id="passenger-first-name" maxlength="50" required>
                                <span class="error-message" id="passenger-first-name-error">Please enter a valid first name (alphabets only)</span>
                            </div>
                            <div class="form-group">
                                <label for="passenger-last-name">Last Name</label>
                                <input type="text" id="passenger-last-name" maxlength="50" required>
                                <span class="error-message" id="passenger-last-name-error">Please enter a valid last name (alphabets only)</span>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="passenger-mobile">Mobile Number</label>
                                <input type="tel" id="passenger-mobile" maxlength="10" required placeholder="Enter 10-digit mobile number">
                                <span class="error-message" id="passenger-mobile-error">Please enter a valid Indian mobile number (10 digits starting with 6, 7, 8, or 9)</span>
                            </div>
                            <div class="form-group">
                                <label for="passenger-email">Email Address</label>
                                <input type="email" id="passenger-email" required>
                                <span class="error-message" id="passenger-email-error">Please enter a valid email address</span>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="seat-class">Seat Class</label>
                                <select id="seat-class" required>
                                    <option value="">Select Class</option>
                                    <option value="Economy">Economy</option>
                                    <option value="Business">Business</option>
                                    <option value="Executive">Executive</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="num-passengers">Number of Passengers</label>
                                <input type="number" id="num-passengers" min="1" max="9" value="1" required>
                                <span class="error-message" id="num-passengers-error">Please enter a valid number of passengers (1-9)</span>
                            </div>
                        </div>
                    </div>

                    <div class="payment-section">
                        <h3 class="payment-title">Payment Information</h3>
                        
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="payment-method">Payment Method</label>
                                <select id="payment-method" required onchange="togglePaymentFields()">
                                    <option value="">Select Payment Method</option>
                                    <option value="Credit Card">Credit Card</option>
                                    <option value="Debit Card">Debit Card</option>
                                    <option value="UPI">UPI</option>
                                    <option value="Net Banking">Net Banking</option>
                                </select>
                            </div>
                        </div>
                        
                        <div id="card-details" style="display: none;">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="card-number">Card Number</label>
                                    <input type="text" id="card-number" maxlength="19" placeholder="1234 5678 9012 3456">
                                    <span class="error-message" id="card-number-error">Please enter a valid card number (16 digits)</span>
                                </div>
                                <div class="form-group">
                                    <label for="card-holder-name">Card Holder Name</label>
                                    <input type="text" id="card-holder-name" maxlength="50">
                                    <span class="error-message" id="card-holder-name-error">Please enter a valid name (alphabets only)</span>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="card-expiry">Expiry Date (MM/YY)</label>
                                    <input type="text" id="card-expiry" maxlength="5" placeholder="MM/YY">
                                    <span class="error-message" id="card-expiry-error">Please enter a valid expiry date (MM/YY)</span>
                                </div>
                                <div class="form-group">
                                    <label for="card-cvv">CVV</label>
                                    <input type="text" id="card-cvv" maxlength="4" placeholder="123">
                                    <span class="error-message" id="card-cvv-error">Please enter a valid CVV (3-4 digits)</span>
                                </div>
                            </div>
                        </div>
                        
                        <div id="upi-details" style="display: none;">
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label for="upi-id">UPI ID</label>
                                    <input type="text" id="upi-id" placeholder="username@paytm">
                                    <span class="error-message" id="upi-id-error">Please enter a valid UPI ID</span>
                                </div>
                            </div>
                        </div>
                        
                        <div id="netbanking-details" style="display: none;">
                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label for="bank-name">Select Bank</label>
                                    <select id="bank-name">
                                        <option value="">Select Bank</option>
                                        <option value="SBI">State Bank of India</option>
                                        <option value="HDFC">HDFC Bank</option>
                                        <option value="ICICI">ICICI Bank</option>
                                        <option value="Axis">Axis Bank</option>
                                        <option value="Kotak">Kotak Mahindra Bank</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="amount-display">
                            <strong>Total Amount: <span id="total-amount">₹0</span></strong>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary">Confirm Booking</button>
                        <button type="button" class="btn btn-secondary" onclick="closeBookingModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Mock data for demonstration
        let carriers = [
            {
                name: "Air India",
                discounts: { "30days": 5, "60days": 10, "90days": 15, "bulk": 12, "silver": 8, "gold": 15, "platinum": 20 },
                refunds: { "2days": 25, "10days": 50, "20days": 85 }
            },
            {
                name: "IndiGo",
                discounts: { "30days": 3, "60days": 8, "90days": 12, "bulk": 10, "silver": 6, "gold": 12, "platinum": 18 },
                refunds: { "2days": 20, "10days": 45, "20days": 80 }
            },
            {
                name: "SpiceJet",
                discounts: { "30days": 4, "60days": 9, "90days": 13, "bulk": 11, "silver": 7, "gold": 13, "platinum": 19 },
                refunds: { "2days": 22, "10days": 47, "20days": 82 }
            },
            {
                name: "Vistara",
                discounts: { "30days": 6, "60days": 11, "90days": 16, "bulk": 13, "silver": 9, "gold": 16, "platinum": 22 },
                refunds: { "2days": 30, "10days": 55, "20days": 90 }
            }
        ];

        let flights = [
            { id: "FL001", carrier: "Air India", origin: "Mumbai", destination: "Delhi", fare: 5500, business: 12, economy: 180, executive: 24 },
            { id: "FL002", carrier: "IndiGo", origin: "Delhi", destination: "Bangalore", fare: 4200, business: 8, economy: 160, executive: 16 },
            { id: "FL003", carrier: "SpiceJet", origin: "Chennai", destination: "Mumbai", fare: 3800, business: 6, economy: 150, executive: 12 },
            { id: "FL004", carrier: "Vistara", origin: "Bangalore", destination: "Kolkata", fare: 4800, business: 10, economy: 170, executive: 20 },
            { id: "FL005", carrier: "Air India", origin: "Delhi", destination: "Chennai", fare: 6200, business: 14, economy: 200, executive: 28 },
            { id: "FL006", carrier: "IndiGo", origin: "Mumbai", destination: "Kolkata", fare: 4900, business: 10, economy: 175, executive: 18 }
        ];

        let users = [];
        let selectedFlight = null;
        let selectedTravelDate = null;

        // Enhanced Validation functions
        function validateAlphabetsOnly(value) {
            return /^[A-Za-z\s]+$/.test(value.trim());
        }

        function validateMobileNumber(value) {
            return /^[6-9]\d{9}$/.test(value);
        }

        function validateEmail(value) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
        }

        function validatePassword(value) {
            return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(value);
        }

        function validateCardNumber(value) {
            const cleanNumber = value.replace(/\s/g, '');
            return /^\d{16}$/.test(cleanNumber);
        }

        function validateCVV(value) {
            return /^\d{3,4}$/.test(value);
        }

        function validateExpiryDate(value) {
            if (!/^\d{2}\/\d{2}$/.test(value)) return false;
            
            const [month, year] = value.split('/').map(num => parseInt(num));
            if (month < 1 || month > 12) return false;
            
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear() % 100;
            const currentMonth = currentDate.getMonth() + 1;
            
            return year > currentYear || (year === currentYear && month >= currentMonth);
        }

        function validateUPI(value) {
            return /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+$/.test(value);
        }

        function validateAge(dateOfBirth) {
            const today = new Date();
            const birthDate = new Date(dateOfBirth);
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                return age - 1 >= 18;
            }
            return age >= 18;
        }

        function formatCardNumber(value) {
            return value.replace(/\s/g, '').replace(/(.{4})/g, '$1 ').trim();
        }

        function formatExpiryDate(value) {
            value = value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            return value;
        }

        function showError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + '-error');
            
            if (field) field.classList.add('error');
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
        }

        function clearError(fieldId) {
            const field = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + '-error');
            
            if (field) field.classList.remove('error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        }

        function showSuccess(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.style.display = 'block';
                
                setTimeout(() => {
                    element.style.display = 'none';
                }, 5000);
            }
        }

        // Navigation function
        function showPage(pageId) {
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.classList.remove('active'));
            
            document.getElementById(pageId).classList.add('active');
            
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }

        // Booking Modal Functions
        function showBookingForm(flightId) {
            selectedFlight = flights.find(f => f.id === flightId);
            if (selectedFlight) {
                selectedTravelDate = document.getElementById('travel-date').value;
                
                // Populate flight information
                document.getElementById('selected-flight-info').innerHTML = `
                    <div class="flight-header">
                        <div class="flight-route">${selectedFlight.origin} → ${selectedFlight.destination}</div>
                        <div class="flight-price">₹${selectedFlight.fare}</div>
                    </div>
                    <div class="flight-details">
                        <div class="detail-item">
                            <div class="detail-label">Flight ID</div>
                            <div class="detail-value">${selectedFlight.id}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Carrier</div>
                            <div class="detail-value">${selectedFlight.carrier}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Travel Date</div>
                            <div class="detail-value">${selectedTravelDate}</div>
                        </div>
                    </div>
                `;
                
                document.getElementById('booking-modal').style.display = 'block';
                updateTotalAmount();
            }
        }

        function closeBookingModal() {
            document.getElementById('booking-modal').style.display = 'none';
            selectedFlight = null;
            selectedTravelDate = null;
            document.getElementById('booking-form').reset();
            
            // Hide all payment sections
            document.getElementById('card-details').style.display = 'none';
            document.getElementById('upi-details').style.display = 'none';
            document.getElementById('netbanking-details').style.display = 'none';
        }

        function togglePaymentFields() {
            const paymentMethod = document.getElementById('payment-method').value;
            
            // Hide all payment detail sections
            document.getElementById('card-details').style.display = 'none';
            document.getElementById('upi-details').style.display = 'none';
            document.getElementById('netbanking-details').style.display = 'none';
            
            // Show relevant section based on payment method
            if (paymentMethod === 'Credit Card' || paymentMethod === 'Debit Card') {
                document.getElementById('card-details').style.display = 'block';
            } else if (paymentMethod === 'UPI') {
                document.getElementById('upi-details').style.display = 'block';
            } else if (paymentMethod === 'Net Banking') {
                document.getElementById('netbanking-details').style.display = 'block';
            }
        }

        function updateTotalAmount() {
            if (!selectedFlight) return;
            
            const numPassengers = parseInt(document.getElementById('num-passengers').value) || 1;
            const seatClass = document.getElementById('seat-class').value;
            
            let multiplier = 1;
            if (seatClass === 'Business') multiplier = 1.5;
            else if (seatClass === 'Executive') multiplier = 1.2;
            
            const totalAmount = selectedFlight.fare * numPassengers * multiplier;
            document.getElementById('total-amount').textContent = `₹${totalAmount.toFixed(0)}`;
        }

        function processBooking(event) {
            event.preventDefault();
            
            let isValid = true;
            
            // Validate passenger details
            const passengerFirstName = document.getElementById('passenger-first-name').value.trim();
            if (!validateAlphabetsOnly(passengerFirstName)) {
                showError('passenger-first-name', 'First name must contain only alphabets');
                isValid = false;
            } else {
                clearError('passenger-first-name');
            }
            
            const passengerLastName = document.getElementById('passenger-last-name').value.trim();
            if (!validateAlphabetsOnly(passengerLastName)) {
                showError('passenger-last-name', 'Last name must contain only alphabets');
                isValid = false;
            } else {
                clearError('passenger-last-name');
            }
            
            const passengerMobile = document.getElementById('passenger-mobile').value.trim();
            if (!validateMobileNumber(passengerMobile)) {
                showError('passenger-mobile', 'Please enter a valid Indian mobile number');
                isValid = false;
            } else {
                clearError('passenger-mobile');
            }
            
            const passengerEmail = document.getElementById('passenger-email').value.trim();
            if (!validateEmail(passengerEmail)) {
                showError('passenger-email', 'Please enter a valid email address');
                isValid = false;
            } else {
                clearError('passenger-email');
            }
            
            // Validate payment details based on selected method
            const paymentMethod = document.getElementById('payment-method').value;
            
            if (paymentMethod === 'Credit Card' || paymentMethod === 'Debit Card') {
                const cardNumber = document.getElementById('card-number').value.trim();
                if (!validateCardNumber(cardNumber)) {
                    showError('card-number', 'Please enter a valid 16-digit card number');
                    isValid = false;
                } else {
                    clearError('card-number');
                }
                
                const cardHolderName = document.getElementById('card-holder-name').value.trim();
                if (!validateAlphabetsOnly(cardHolderName)) {
                    showError('card-holder-name', 'Card holder name must contain only alphabets');
                    isValid = false;
                } else {
                    clearError('card-holder-name');
                }
                
                const cardExpiry = document.getElementById('card-expiry').value.trim();
                if (!validateExpiryDate(cardExpiry)) {
                    showError('card-expiry', 'Please enter a valid future expiry date (MM/YY)');
                    isValid = false;
                } else {
                    clearError('card-expiry');
                }
                
                const cardCVV = document.getElementById('card-cvv').value.trim();
                if (!validateCVV(cardCVV)) {
                    showError('card-cvv', 'Please enter a valid CVV (3-4 digits)');
                    isValid = false;
                } else {
                    clearError('card-cvv');
                }
            } else if (paymentMethod === 'UPI') {
                const upiId = document.getElementById('upi-id').value.trim();
                if (!validateUPI(upiId)) {
                    showError('upi-id', 'Please enter a valid UPI ID');
                    isValid = false;
                } else {
                    clearError('upi-id');
                }
            }
            
            if (isValid) {
                const bookingId = 'BKG' + String(Math.floor(Math.random() * 100000)).padStart(5, '0');
                const totalAmount = document.getElementById('total-amount').textContent;
                
                showSuccess('booking-success', `Booking Confirmed! Booking ID: ${bookingId}. Total Amount: ${totalAmount}`);
                closeBookingModal();
            }
        }

        // User Registration functionality
        function registerUser(event) {
            event.preventDefault();
            
            let isValid = true;
            
            // Validate first name
            const firstName = document.getElementById('user-first-name').value.trim();
            if (!validateAlphabetsOnly(firstName)) {
                showError('user-first-name', 'First name must contain only alphabets');
                isValid = false;
            } else {
                clearError('user-first-name');
            }
            
            // Validate last name
            const lastName = document.getElementById('user-last-name').value.trim();
            if (!validateAlphabetsOnly(lastName)) {
                showError('user-last-name', 'Last name must contain only alphabets');
                isValid = false;
            } else {
                clearError('user-last-name');
            }
            
            // Validate email
            const email = document.getElementById('user-email').value.trim();
            if (!validateEmail(email)) {
                showError('user-email', 'Please enter a valid email address');
                isValid = false;
            } else {
                clearError('user-email');
            }
            
            // Validate mobile number
            const mobile = document.getElementById('user-mobile').value.trim();
            if (!validateMobileNumber(mobile)) {
                showError('user-mobile', 'Please enter a valid Indian mobile number (10 digits starting with 6, 7, 8, or 9)');
                isValid = false;
            } else {
                clearError('user-mobile');
            }
            
            // Validate date of birth (age requirement)
            const dob = document.getElementById('user-dob').value;
            if (!validateAge(dob)) {
                showError('user-dob', 'You must be at least 18 years old to register');
                isValid = false;
            } else {
                clearError('user-dob');
            }
            
            // Validate password
            const password = document.getElementById('user-password').value;
            if (!validatePassword(password)) {
                showError('user-password', 'Password must meet all requirements');
                isValid = false;
            } else {
                clearError('user-password');
            }
            
            // Validate confirm password
            const confirmPassword = document.getElementById('user-confirm-password').value;
            if (password !== confirmPassword) {
                showError('user-confirm-password', 'Passwords do not match');
                isValid = false;
            } else {
                clearError('user-confirm-password');
            }
            
            if (isValid) {
                const userId = 'USR' + String(Math.floor(Math.random() * 10000)).padStart(4, '0');
                
                users.push({
                    id: userId,
                    firstName: firstName,
                    lastName: lastName,
                    email: email,
                    mobile: mobile,
                    dob: dob,
                    gender: document.getElementById('user-gender').value,
                    username: document.getElementById('user-username').value,
                    userType: document.getElementById('user-type').value
                });
                
                showSuccess('register-success', `User Registration Successful! User ID: ${userId}`);
                event.target.reset();
            }
        }

        // Add Carrier functionality
        function addCarrier(event) {
            event.preventDefault();
            
            let isValid = true;
            
            // Validate carrier name
            const carrierName = document.getElementById('carrier-name').value.trim();
            if (!validateAlphabetsOnly(carrierName)) {
                showError('carrier-name', 'Carrier name must contain only alphabets');
                isValid = false;
            } else {
                clearError('carrier-name');
            }
            
            // Validate all percentage fields
            const percentageFields = [
                'discount-30days', 'discount-60days', 'discount-90days', 'discount-bulk',
                'discount-silver', 'discount-gold', 'discount-platinum',
                'refund-2days', 'refund-10days', 'refund-20days'
            ];
            
            percentageFields.forEach(fieldId => {
                const value = parseFloat(document.getElementById(fieldId).value);
                if (isNaN(value) || value < 0 || value > 100) {
                    showError(fieldId, 'Please enter a valid percentage between 0 and 100');
                    isValid = false;
                } else {
                    clearError(fieldId);
                }
            });
            
            if (isValid) {
                const carrierId = 'CAR' + String(Math.floor(Math.random() * 1000)).padStart(3, '0');
                
                carriers.push({
                    name: carrierName,
                    id: carrierId,
                    discounts: {
                        "30days": parseFloat(document.getElementById('discount-30days').value),
                        "60days": parseFloat(document.getElementById('discount-60days').value),
                        "90days": parseFloat(document.getElementById('discount-90days').value),
                        "bulk": parseFloat(document.getElementById('discount-bulk').value),
                        "silver": parseFloat(document.getElementById('discount-silver').value),
                        "gold": parseFloat(document.getElementById('discount-gold').value),
                        "platinum": parseFloat(document.getElementById('discount-platinum').value)
                    },
                    refunds: {
                        "2days": parseFloat(document.getElementById('refund-2days').value),
                        "10days": parseFloat(document.getElementById('refund-10days').value),
                        "20days": parseFloat(document.getElementById('refund-20days').value)
                    }
                });
                
                showSuccess('add-carrier-success', `Carrier Information Saved Successfully!!! - Carrier Id generated is ${carrierId}`);
                event.target.reset();
            }
        }

        // Search Carrier functionality
        function searchCarrier(event) {
            event.preventDefault();
            
            const selectedCarrier = document.getElementById('edit-carrier-select').value;
            const carrier = carriers.find(c => c.name === selectedCarrier);
            
            if (carrier) {
                document.getElementById('edit-carrier-name').value = carrier.name;
                document.getElementById('edit-discount-60days').value = carrier.discounts["60days"];
                document.getElementById('edit-discount-90days').value = carrier.discounts["90days"];
                document.getElementById('edit-discount-bulk').value = carrier.discounts.bulk;
                document.getElementById('edit-discount-silver').value = carrier.discounts.silver;
                document.getElementById('edit-discount-gold').value = carrier.discounts.gold;
                document.getElementById('edit-discount-platinum').value = carrier.discounts.platinum;
                document.getElementById('edit-refund-2days').value = carrier.refunds["2days"];
                document.getElementById('edit-refund-10days').value = carrier.refunds["10days"];
                document.getElementById('edit-refund-20days').value = carrier.refunds["20days"];
                
                document.getElementById('carrier-edit-form').classList.add('show');
            }
        }

        // Update Carrier functionality
        function updateCarrier(event) {
            event.preventDefault();
            
            const carrierName = document.getElementById('edit-carrier-name').value;
            const carrierIndex = carriers.findIndex(c => c.name === carrierName);
            
            if (carrierIndex !== -1) {
                carriers[carrierIndex].discounts = {
                    "60days": parseFloat(document.getElementById('edit-discount-60days').value),
                    "90days": parseFloat(document.getElementById('edit-discount-90days').value),
                    "bulk": parseFloat(document.getElementById('edit-discount-bulk').value),
                    "silver": parseFloat(document.getElementById('edit-discount-silver').value),
                    "gold": parseFloat(document.getElementById('edit-discount-gold').value),
                    "platinum": parseFloat(document.getElementById('edit-discount-platinum').value)
                };
                
                carriers[carrierIndex].refunds = {
                    "2days": parseFloat(document.getElementById('edit-refund-2days').value),
                    "10days": parseFloat(document.getElementById('edit-refund-10days').value),
                    "20days": parseFloat(document.getElementById('edit-refund-20days').value)
                };
                
                showSuccess('edit-carrier-success', `${carrierName} details are updated Successfully`);
            }
        }

        // Delete Carrier functionality
        function deleteCarrier() {
            const carrierName = document.getElementById('edit-carrier-name').value;
            const carrierIndex = carriers.findIndex(c => c.name === carrierName);
            
            if (carrierIndex !== -1) {
                carriers.splice(carrierIndex, 1);
                showSuccess('edit-carrier-success', 'Carrier Detail is removed from the System');
                document.getElementById('carrier-edit-form').classList.remove('show');
            }
        }

        // Add Flight functionality
        function addFlight(event) {
            event.preventDefault();
            
            let isValid = true;
            
            const numericFields = ['flight-airfare', 'seat-business', 'seat-economy', 'seat-executive'];
            
            numericFields.forEach(fieldId => {
                const value = parseInt(document.getElementById(fieldId).value);
                if (isNaN(value) || value < 0) {
                    showError(fieldId, 'Please enter a valid number');
                    isValid = false;
                } else {
                    clearError(fieldId);
                }
            });
            
            const origin = document.getElementById('flight-origin').value;
            const destination = document.getElementById('flight-destination').value;
            
            if (origin === destination) {
                alert('Origin and destination cannot be the same');
                isValid = false;
            }
            
            if (isValid) {
                const flightId = 'FL' + String(Math.floor(Math.random() * 1000)).padStart(3, '0');
                
                flights.push({
                    id: flightId,
                    carrier: document.getElementById('flight-carrier').value,
                    origin: origin,
                    destination: destination,
                    fare: parseInt(document.getElementById('flight-airfare').value),
                    business: parseInt(document.getElementById('seat-business').value),
                    economy: parseInt(document.getElementById('seat-economy').value),
                    executive: parseInt(document.getElementById('seat-executive').value)
                });
                
                showSuccess('add-flight-success', `Flight Information Saved Successfully!!! - Flight Id generated is ${flightId}`);
                event.target.reset();
            }
        }

        // Search Flight functionality
        function searchFlight(event) {
            event.preventDefault();
            
            const carrier = document.getElementById('search-flight-carrier').value;
            const origin = document.getElementById('search-flight-origin').value;
            const destination = document.getElementById('search-flight-destination').value;
            
            const flight = flights.find(f => 
                f.carrier === carrier && f.origin === origin && f.destination === destination
            );
            
            if (flight) {
                document.getElementById('edit-flight-carrier').value = flight.carrier;
                document.getElementById('edit-flight-origin').value = flight.origin;
                document.getElementById('edit-flight-destination').value = flight.destination;
                document.getElementById('edit-flight-airfare').value = flight.fare;
                document.getElementById('edit-seat-business').value = flight.business;
                document.getElementById('edit-seat-economy').value = flight.economy;
                document.getElementById('edit-seat-executive').value = flight.executive;
                
                document.getElementById('flight-search-results').classList.add('show');
            } else {
                alert('No flight found matching the criteria');
            }
        }

        // Update Flight functionality
        function updateFlight(event) {
            event.preventDefault();
            
            const carrier = document.getElementById('edit-flight-carrier').value;
            const origin = document.getElementById('edit-flight-origin').value;
            const destination = document.getElementById('edit-flight-destination').value;
            
            const flightIndex = flights.findIndex(f => 
                f.carrier === carrier && f.origin === origin && f.destination === destination
            );
            
            if (flightIndex !== -1) {
                flights[flightIndex].fare = parseInt(document.getElementById('edit-flight-airfare').value);
                flights[flightIndex].business = parseInt(document.getElementById('edit-seat-business').value);
                flights[flightIndex].economy = parseInt(document.getElementById('edit-seat-economy').value);
                flights[flightIndex].executive = parseInt(document.getElementById('edit-seat-executive').value);
                
                showSuccess('edit-flight-success', 'Flight Information Updated Successfully!!!');
            }
        }

        // Delete Flight functionality
        function deleteFlight() {
            const carrier = document.getElementById('edit-flight-carrier').value;
            const origin = document.getElementById('edit-flight-origin').value;
            const destination = document.getElementById('edit-flight-destination').value;
            
            const flightIndex = flights.findIndex(f => 
                f.carrier === carrier && f.origin === origin && f.destination === destination
            );
            
            if (flightIndex !== -1) {
                flights.splice(flightIndex, 1);
                showSuccess('edit-flight-success', 'Flight details are removed Successfully!!!');
                document.getElementById('flight-search-results').classList.remove('show');
            }
        }

        // Search Flights for booking
        function searchFlights(event) {
            event.preventDefault();
            
            const origin = document.getElementById('search-origin').value;
            const destination = document.getElementById('search-destination').value;
            const travelDate = document.getElementById('travel-date').value;
            
            if (origin === destination) {
                alert('Origin and destination cannot be the same');
                return;
            }
            
            const availableFlights = flights.filter(f => 
                f.origin === origin && f.destination === destination
            );
            
            const flightsContainer = document.getElementById('flights-container');
            
            if (availableFlights.length > 0) {
                flightsContainer.innerHTML = availableFlights.map(flight => `
                    <div class="flight-card">
                        <div class="flight-header">
                            <div class="flight-route">${flight.origin} → ${flight.destination}</div>
                            <div class="flight-price">₹${flight.fare}</div>
                        </div>
                        <div class="flight-details">
                            <div class="detail-item">
                                <div class="detail-label">Flight ID</div>
                                <div class="detail-value">${flight.id}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Carrier</div>
                                <div class="detail-value">${flight.carrier}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Business</div>
                                <div class="detail-value">${flight.business} seats</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Economy</div>
                                <div class="detail-value">${flight.economy} seats</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Executive</div>
                                <div class="detail-value">${flight.executive} seats</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Travel Date</div>
                                <div class="detail-value">${travelDate}</div>
                            </div>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="showBookingForm('${flight.id}')">Book Flight</button>
                        </div>
                    </div>
                `).join('');
                
                document.getElementById('flight-results').classList.add('show');
            } else {
                flightsContainer.innerHTML = '<p>No flights available for the selected route.</p>';
                document.getElementById('flight-results').classList.add('show');
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const dateInputs = ['travel-date', 'user-dob'];
            
            dateInputs.forEach(inputId => {
                const dateInput = document.getElementById(inputId);
                if (dateInput) {
                    if (inputId === 'travel-date') {
                        dateInput.min = today;
                    } else if (inputId === 'user-dob') {
                        const maxDate = new Date();
                        maxDate.setFullYear(maxDate.getFullYear() - 18);
                        dateInput.max = maxDate.toISOString().split('T')[0];
                    }
                }
            });
            
            // Add real-time validation listeners for name fields
            const nameFields = [
                'carrier-name', 'user-first-name', 'user-last-name', 
                'passenger-first-name', 'passenger-last-name', 'card-holder-name'
            ];
            nameFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('input', function() {
                        if (this.value && !validateAlphabetsOnly(this.value)) {
                            showError(fieldId, 'Only alphabets and spaces are allowed');
                        } else {
                            clearError(fieldId);
                        }
                    });
                }
            });
            
            // Add real-time validation for mobile numbers
            const mobileFields = ['user-mobile', 'passenger-mobile'];
            mobileFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('input', function() {
                        this.value = this.value.replace(/\D/g, '').substring(0, 10);
                        
                        if (this.value.length === 10) {
                            if (!validateMobileNumber(this.value)) {
                                showError(fieldId, 'Mobile number must start with 6, 7, 8, or 9');
                            } else {
                                clearError(fieldId);
                                this.classList.add('validation-success');
                            }
                        } else if (this.value.length > 0) {
                            clearError(fieldId);
                            this.classList.remove('validation-success');
                        }
                    });
                }
            });
            
            // Add real-time validation for email fields
            const emailFields = ['user-email', 'passenger-email'];
            emailFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('blur', function() {
                        if (this.value && !validateEmail(this.value)) {
                            showError(fieldId, 'Please enter a valid email address');
                        } else if (this.value) {
                            clearError(fieldId);
                            this.classList.add('validation-success');
                        }
                    });
                }
            });
            
            // Add real-time validation for password
            const passwordField = document.getElementById('user-password');
            if (passwordField) {
                passwordField.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        if (!validatePassword(this.value)) {
                            this.classList.add('validation-warning');
                            this.classList.remove('validation-success');
                        } else {
                            this.classList.add('validation-success');
                            this.classList.remove('validation-warning');
                            clearError('user-password');
                        }
                    } else {
                        this.classList.remove('validation-warning', 'validation-success');
                    }
                });
            }
            
            // Add real-time validation for confirm password
            const confirmPasswordField = document.getElementById('user-confirm-password');
            if (confirmPasswordField) {
                confirmPasswordField.addEventListener('input', function() {
                    const password = document.getElementById('user-password').value;
                    if (this.value.length > 0) {
                        if (this.value !== password) {
                            showError('user-confirm-password', 'Passwords do not match');
                        } else {
                            clearError('user-confirm-password');
                            this.classList.add('validation-success');
                        }
                    } else {
                        clearError('user-confirm-password');
                        this.classList.remove('validation-success');
                    }
                });
            }
            
            // Add formatting for card number
            const cardNumberField = document.getElementById('card-number');
            if (cardNumberField) {
                cardNumberField.addEventListener('input', function() {
                    let value = this.value.replace(/\D/g, '').substring(0, 16);
                    this.value = formatCardNumber(value);
                    
                    if (value.length === 16) {
                        if (validateCardNumber(this.value)) {
                            clearError('card-number');
                            this.classList.add('validation-success');
                        }
                    } else if (value.length > 0) {
                        this.classList.remove('validation-success');
                    }
                });
            }
            
            // Add formatting for card expiry
            const cardExpiryField = document.getElementById('card-expiry');
            if (cardExpiryField) {
                cardExpiryField.addEventListener('input', function() {
                    this.value = formatExpiryDate(this.value);
                    
                    if (this.value.length === 5) {
                        if (validateExpiryDate(this.value)) {
                            clearError('card-expiry');
                            this.classList.add('validation-success');
                        } else {
                            showError('card-expiry', 'Please enter a valid future date');
                        }
                    } else {
                        this.classList.remove('validation-success');
                        clearError('card-expiry');
                    }
                });
            }
            
            // Add validation for CVV
            const cardCVVField = document.getElementById('card-cvv');
            if (cardCVVField) {
                cardCVVField.addEventListener('input', function() {
                    this.value = this.value.replace(/\D/g, '').substring(0, 4);
                    
                    if (this.value.length >= 3) {
                        if (validateCVV(this.value)) {
                            clearError('card-cvv');
                            this.classList.add('validation-success');
                        }
                    } else {
                        this.classList.remove('validation-success');
                    }
                });
            }
            
            // Add validation for UPI ID
            const upiField = document.getElementById('upi-id');
            if (upiField) {
                upiField.addEventListener('blur', function() {
                    if (this.value && validateUPI(this.value)) {
                        clearError('upi-id');
                        this.classList.add('validation-success');
                    } else if (this.value) {
                        showError('upi-id', 'Please enter a valid UPI ID (e.g., username@paytm)');
                    }
                });
            }
            
            // Add listeners for booking form amount calculation
            const amountFields = ['num-passengers', 'seat-class'];
            amountFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('change', updateTotalAmount);
                }
            });
            
            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                const modal = document.getElementById('booking-modal');
                if (event.target === modal) {
                    closeBookingModal();
                }
            });
        });
    </script>
</body>
</html>